import type { User, IConsultant, <PERSON><PERSON>d<PERSON><PERSON>, ICarrier, IContact, ILocation } from '@/types';
import type { IAssignmentCategory } from './assignment_category';
import type { IComment } from './comment';

export interface IAssignment {
  id: number;
  // IDS
  primaryContactId: number | null;
  secondaryContactId: number | null;
  lossLocationId: number;
  adjusterId: number;
  assignmentLeadId: number;
  createdById: number;

  // Detailed Information
  status: string;
  claimNumber: string;
  insuredName: string;
  assignmentName: string;
  dateOfLoss: string;
  claimDetail: string | null;
  assignmentDescription: string | null;

  // Relationships
  createdBy?: User;
  assignmentLead?: IConsultant;
  adjuster?: IAdjuster;
  carriers?: ICarrier[];
  categoryData?: IAssignmentCategory[];
  primaryContact?: IContact;
  secondaryContact?: IContact;
  lossLocation?: ILocation;
  comments?: IComment[];

  createdAt: string;
  updatedAt: string;
};