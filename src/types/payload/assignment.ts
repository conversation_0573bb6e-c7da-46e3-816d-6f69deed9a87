export type IAssignmentCreatePayload = {
    primaryContactId: number | null;
    secondaryContactId: number | null;
    lossLocationId: number;
    adjusterId: number;
    assignmentLeadId: number;

    // Detailed Information
    status: string;
    claimNumber: string;
    insuredName: string;
    assignmentName: string;
    dateOfLoss: string;
    claimDetail: string | null;
    assignmentDescription: string | null;

    carriers: number[];
};

export interface IAssignmentUpdatePayload extends Omit<IAssignmentCreatePayload, "createdById"> {
    createdAt?: string;
    updatedAt?: string;
}