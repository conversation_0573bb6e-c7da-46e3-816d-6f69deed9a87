import { z } from "zod";
import { useToast } from "@/components/custom-ui/toast";
import { useBusinessContext } from "@/context/BusinessContext";
import { useUserContext } from "@/context/UserContext";
import type { ICarrier, ICarrierCreatePayload, ICarrierUpdatePayload } from "@/types";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

const carrierFormSchema = z.object({
  id: z.number().optional(),
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Invalid email").min(1, "Email is required"),
  phone: z.string().min(1, "Phone number is required"),
  adjusterId: z.number().optional(),
})

interface CarrierFormProps {
  initialData?: ICarrier;
  mode?: "create" | "edit";
  onCancel: () => void;
}

export function CarrierForm({ initialData, onCancel, mode = "create" }: CarrierFormProps) {
  const { user } = useUserContext();
  const {
    entityActions: {
      createEntity,
      updateEntity
    },
    fetchActions: {
      fetchCarriers,
    },
    entities: {
      adjusters,
    },
  } = useBusinessContext();
  const { addToast } = useToast();

  const form = useForm<z.infer<typeof carrierFormSchema>>({
    resolver: zodResolver(carrierFormSchema),
    defaultValues: {
      id: initialData?.id || 0,
      name: initialData?.name || "",
      email: initialData?.email || "",
      phone: initialData?.phone || "",
      adjusterId: initialData?.adjusterId || 0,
    },
  })

  const handleSubmit = async (data: z.infer<typeof carrierFormSchema>) => {
    if (!user) return addToast({
      type: "error",
      title: "Carrier creation failed",
      description: "You must be logged in to create a carrier."
    });

    if (mode === "create") {
      handleCreate(data);
    } else {
      handleEdit(data);
    }
  }

  const handleCreate = async (data: z.infer<typeof carrierFormSchema>) => {
    const payload: ICarrierCreatePayload = {
      name: data.name,
      email: data.email,
      phone: data.phone,
      ...(data.adjusterId !== 0 && { adjusterId: data.adjusterId }),
    };
    console.log("Payload:", payload);
    const response = await createEntity<ICarrierCreatePayload, ICarrier>("/v1/carriers", payload);
    if (response.error || !response.data) {
      console.log('Failed to create carrier:', response.error);
      addToast({
        type: "error",
        title: "Carrier creation failed",
        description: response.error || "Failed to create carrier"
      });
      return;
    }
    addToast({
      type: "success",
      title: "Carrier created",
      description: "The carrier has been created successfully."
    })
    fetchCarriers()
    onCancel();
  }

  const handleEdit = async (data: z.infer<typeof carrierFormSchema>) => {
    if (!initialData || !data.id) return addToast({
      type: "error",
      title: "Carrier update failed",
      description: "Failed to update carrier. Carrier ID is missing."
    });

    const payload: ICarrierUpdatePayload = {
      id: data.id,
      name: data.name,
      email: data.email,
      phone: data.phone,
      ...(data.adjusterId !== 0 && { adjusterId: data.adjusterId }),
    };
    console.log("Payload:", payload);
    const response = await updateEntity<ICarrierCreatePayload, ICarrier>(`/v1/carriers/${initialData.id}`, payload);
    if (response.error || !response.data) {
      console.log('Failed to update carrier:', response.error);
      addToast({
        type: "error",
        title: "Carrier update failed",
        description: response.error || "Failed to update carrier"
      });
      return;
    }
    addToast({
      type: "success",
      title: "Carrier updated",
      description: "The carrier has been updated successfully."
    })
    fetchCarriers()
    onCancel();
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Name</FormLabel>
                <FormControl>
                  <Input placeholder="Enter name" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="phone"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Phone</FormLabel>
                <FormControl>
                  <Input placeholder="Enter phone number" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input placeholder="Enter email" type="email" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="adjusterId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Adjuster</FormLabel>
                <FormControl>
                  <Select
                    value={field.value && field.value !== 0 ? field.value.toString() : ""}
                    onValueChange={(value) => field.onChange(parseInt(value))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select adjuster" />
                    </SelectTrigger>
                    <SelectContent>
                      {adjusters.map((adjuster) => (
                        <SelectItem key={adjuster.id} value={adjuster.id.toString()}>
                          {adjuster.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <div className="flex justify-end gap-2 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
          >
            Cancel
          </Button>
          <Button
            type="submit"
          >
            Save
          </Button>
        </div>
      </form>
    </Form>
  )
}