import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { useToast } from "@/components/custom-ui/toast";
import { useBusinessContext } from "@/context/BusinessContext";
import { useUserContext } from "@/context/UserContext";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import type { ICategory, ICategoryCreatePayload, ICategoryUpdatePayload } from "@/types";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

export const categoryFormSchema = z.object({
	id: z.number().optional(),
	name: z.string().min(1, "Name is required"),
})

interface CategoryFormProps {
	initialData?: ICategory;
	mode?: "create" | "edit";
	onCancel: () => void;
}

export function CategoryForm({ initialData, onCancel, mode = "create" }: CategoryFormProps) {
	const { user } = useUserContext();
	const {
		entityActions: {
			createEntity,
			updateEntity
		},
		fetchActions: {
			fetchCategories
		}
	} = useBusinessContext()
	const { addToast } = useToast();

	const form = useForm<z.infer<typeof categoryFormSchema>>({
		resolver: zodResolver(categoryFormSchema),
		defaultValues: {
			id: initialData?.id || 0,
			name: initialData?.name || "",
		}
	})

	const handleSubmit = async (data: z.infer<typeof categoryFormSchema>) => {
		if (!user) return addToast({
			type: "error",
			title: "Category creation failed",
			description: "You must be logged in to create a category."
		});

		if (mode === "create") {
			handleCreate(data);
		} else {
			handleEdit(data);
		}
	}

	const handleCreate = async (data: z.infer<typeof categoryFormSchema>) => {
		const payload: ICategoryCreatePayload = {
			name: data.name
		}

		const response = await createEntity<ICategoryCreatePayload, ICategory>("/v1/categories", payload)
		if (response.error) return addToast({
			type: "error",
			title: "Category creation failed",
			description: response.error || "Failed to create category"
		})
		addToast({
			type: "success",
			title: "Category created",
			description: "The category has been created successfully."
		})
		fetchCategories()
		onCancel();
	}

	const handleEdit = async (data: z.infer<typeof categoryFormSchema>) => {
		if (!initialData || !data.id) return addToast({
			type: "error",
			title: "Category update failed",
			description: "Failed to update category. Category ID is missing."
		});

		const payload: ICategoryUpdatePayload = {
			id: data.id,
			name: data.name,
		}
		const response = await updateEntity<ICategoryUpdatePayload, ICategory>(`/v1/categories/${initialData.id}`, payload)
		if (response.error) return addToast({
			type: "error",
			title: "Category update failed",
			description: response.error || "Failed to update category"
		})
		addToast({
			type: "success",
			title: "Category updated",
			description: "The category has been updated successfully."
		})
		fetchCategories()
		onCancel();
	}

	return (
		<Form {...form}>
			<form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
				<FormField
					control={form.control}
					name="name"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Name</FormLabel>
							<FormControl>
								<Input placeholder="Enter name" {...field} />
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				<div className="flex justify-end gap-2 pt-4">
					<Button
						type="button"
						variant="outline"
						onClick={onCancel}
					>
						Cancel
					</Button>
					<Button
						type="submit"
					>
						Save
					</Button>
				</div>
			</form>
		</Form>
	)
}