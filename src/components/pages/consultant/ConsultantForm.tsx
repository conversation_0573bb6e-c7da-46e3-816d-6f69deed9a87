import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useUserContext } from "@/context/UserContext";
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/custom-ui/toast";
import type { IConsultantCreatePayload, IConsultantUpdatePayload } from "@/types/payload/consultant";
import type { IConsultant } from "@/types";
import { useBusinessContext } from "@/context/BusinessContext";

export const consultantFormSchema = z.object({
  id: z.number().optional(),
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Invalid email").min(1, "Email is required"),
  phone: z.string().min(1, "Phone number is required"),
})

interface ConsultantFormProps {
  initialData?: IConsultant;
  onCancel: () => void;
  mode?: "create" | "edit";
}

export function ConsultantForm({ initialData, onCancel, mode = "create" }: ConsultantFormProps) {
  const { user } = useUserContext();
  const {
    entityActions: {
      createEntity,
      updateEntity
    },
    fetchActions: {
      fetchConsultants,
    },
  } = useBusinessContext();
  const { addToast } = useToast();

  const form = useForm<z.infer<typeof consultantFormSchema>>({
    resolver: zodResolver(consultantFormSchema),
    defaultValues: {
      id: initialData?.id || 0,
      name: initialData?.name || "",
      email: initialData?.email || "",
      phone: initialData?.phone || "",
    },
  })

  const handleSubmit = async (data: z.infer<typeof consultantFormSchema>) => {
    if (!user) return addToast({
      type: "error",
      title: "Consultant creation failed",
      description: "You must be logged in to create a consultant."
    });

    if (mode === "create") {
      handleCreate(data);
    } else {
      handleEdit(data);
    }
  }

  const handleCreate = async (data: z.infer<typeof consultantFormSchema>) => {
    const payload: IConsultantCreatePayload = {
      name: data.name,
      email: data.email,
      phone: data.phone,
    };
    const response = await createEntity<IConsultantCreatePayload, IConsultant>("/v1/consultants", payload);
    if (response.error || !response.data) {
      addToast({
        type: "error",
        title: "Consultant creation failed",
        description: response.error || "Failed to create consultant"
      });
      return;
    }
    addToast({
      type: "success",
      title: "Consultant created",
      description: "The consultant has been created successfully."
    })
    console.log("Consultant created successfully:", response.data);
    fetchConsultants()
    onCancel();
  }

  const handleEdit = async (data: z.infer<typeof consultantFormSchema>) => {
    if (!initialData || !data.id) return addToast({
      type: "error",
      title: "Consultant update failed",
      description: "Failed to update consultant. Consultant ID is missing."
    });
    const payload: IConsultantUpdatePayload = {
      id: data.id,
      name: data.name,
      email: data.email,
      phone: data.phone,
    };
    const response = await updateEntity<IConsultantUpdatePayload, IConsultant>(`/v1/consultants/${data.id}`, payload);
    if (response.error || !response.data) {
      addToast({
        type: "error",
        title: "Consultant update failed",
        description: response.error || "Failed to update consultant"
      });
      return;
    }
    addToast({
      type: "success",
      title: "Consultant updated",
      description: "The consultant has been updated successfully."
    })
    console.log("Consultant updated successfully:", response.data);
    fetchConsultants()
    onCancel();
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4 w-full">
        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Name</FormLabel>
                <FormControl>
                  <Input placeholder="Enter name" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="phone"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Phone</FormLabel>
                <FormControl>
                  <Input placeholder="Enter phone number" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input placeholder="Enter email" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="flex justify-end gap-2 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
          >
            Cancel
          </Button>
          <Button
            type="submit"
          >
            Save
          </Button>
        </div>
      </form>
    </Form>
  )
}
