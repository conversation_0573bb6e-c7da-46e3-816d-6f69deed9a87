export interface DashboardMetrics {
  openAssignments: number;
  closedAssignments: number;
  activeConsultations: number;
  totalRevenue: number;
  avgProcessingTime: number;
  carrierPerformance: CarrierPerformance[];
  recentActivity: ActivityItem[];
  assignmentsByStatus: AssignmentStatusCount[];
  assignmentsByMonth: MonthlyAssignmentData[];
  topAdjusters: AdjusterPerformance[];
}

export interface CarrierPerformance {
  carrierId: string;
  carrierName: string;
  totalAssignments: number;
  avgProcessingTime: number;
  satisfactionRating: number;
  revenue: number;
}

export interface ActivityItem {
  id: string;
  type: 'assignment_created' | 'assignment_assigned' | 'assignment_completed' | 'adjuster_added' | 'carrier_updated';
  title: string;
  description: string;
  timestamp: Date;
  userId?: string;
  userName?: string;
}

export interface AssignmentStatusCount {
  status: string;
  count: number;
  percentage: number;
}

export interface MonthlyAssignmentData {
  month: string;
  openAssignments: number;
  closedAssignments: number;
  revenue: number;
}

export interface AdjusterPerformance {
  adjusterId: string;
  adjusterName: string;
  totalAssignments: number;
  avgCompletionTime: number;
  rating: number;
  revenue: number;
}

export type DateRange = 'daily' | 'weekly' | 'monthly' | 'quarterly';
