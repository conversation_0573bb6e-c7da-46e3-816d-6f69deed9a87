import { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, PageContent } from '@/components/custom-ui/page-header';
import { DataTable, type Column } from '@/components/custom-ui/data-table';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Plus, Edit, Trash2, Phone, Mail, User, CalendarArrowUp } from 'lucide-react';
import { CarrierForm } from '@/components/pages/carrier/CarrierForm';
import { useBusinessContext } from '@/context/BusinessContext';
import type { ICarrier } from '@/types';

export function Carriers() {
  const [isCarrierFormDialogOpen, setIsCarrierFormDialogOpen] = useState(false);
  const [editingCarrier, setEditingCarrier] = useState<ICarrier | null>(null);

  const {
    entities: { carriers },
    loading,
    contextError,
    fetchActions: { fetchCarriers },
  } = useBusinessContext();

  useEffect(() => {
    fetchCarriers();
  }, []);

  const columns: Column<ICarrier>[] = [
    {
      key: 'name',
      header: 'Name',
      sortable: true,
      render: (value) => (
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
            <span className="font-semibold text-primary">
              {value.charAt(0).toUpperCase()}
            </span>
          </div>
          <div>
            <div className="font-medium">{value}</div>
          </div>
        </div>
      )
    },
    {
      key: 'email',
      header: 'Contact',
      render: (value, row) => (
        <div className="space-y-1 max-w-[200px]">
          <div className="flex items-center gap-2 text-sm">
            <Mail className="h-3 w-3" />
            <span className="truncate whitespace-nowrap overflow-hidden block">{value}</span>
          </div>
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Phone className="h-3 w-3" />
            <span className="truncate whitespace-nowrap overflow-hidden block">{row.phone}</span>
          </div>
        </div>
      )
    },
    {
      key: 'adjuster',
      header: 'Adjuster',
      render: (_, row) => {
        const adjuster = row.adjuster
        return (
          <div className="flex items-center justify-center gap-2">
            <User className="h-4 w-4 text-muted-foreground" />
            <span className="font-medium">{adjuster?.name || 'Null'}</span>
          </div>
        );
      }
    },
    {
      key: 'createdAt',
      header: 'Created',
      sortable: true,
      render: (value) => (
        <span className="flex items-center gap-2 text-sm text-muted-foreground">
          <CalendarArrowUp className="h-4 w-4 text-muted-foreground" />
          {new Date(value).toLocaleDateString()}
        </span>
      )
    },
    {
      key: 'actions',
      header: 'Actions',
      render: (_, row) => (
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              setEditingCarrier(row);
              setIsCarrierFormDialogOpen(true);
            }}
            className='cursor-pointer'
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              handleDelete(row.id);
            }}
            className='cursor-pointer'
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      )
    }
  ];

  const handleDelete = (id: number) => {
    // TODO: Implement delete functionality with API
    console.log('Delete carrier:', id);
  };

  return (
    <div className="space-y-6">
      <PageHeader
        title="Carriers"
        description="Manage insurance carriers and their information"
      >
        <Button onClick={() => {
          setEditingCarrier(null);
          setIsCarrierFormDialogOpen(true);
        }}>
          <Plus className="h-4 w-4 mr-2" />
          Add Carrier
        </Button>
      </PageHeader>

      <PageContent>
        {contextError && (
          <div className="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
            <p>Error: {contextError}</p>
          </div>
        )}

        <DataTable
          data={carriers}
          columns={columns}
          searchPlaceholder="Search carriers..."
          onRowClick={(carrier) => console.log('View carrier:', carrier)}
          loading={loading.adjusters}
        />
      </PageContent>

      {/* Add/Edit Dialog */}
      <Dialog open={isCarrierFormDialogOpen} onOpenChange={setIsCarrierFormDialogOpen}>
        <DialogContent className="max-w-[90vw] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {editingCarrier ? 'Edit Carrier' : 'Add New Carrier'}
            </DialogTitle>
          </DialogHeader>
          <CarrierForm
            mode={editingCarrier ? "edit" : "create"}
            initialData={editingCarrier || undefined}
            onCancel={() => setIsCarrierFormDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
}
