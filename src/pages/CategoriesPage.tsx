import { useEffect, useState } from 'react';
import { <PERSON>Header, PageContent } from '@/components/custom-ui/page-header';
import { DataTable, type Column } from '@/components/custom-ui/data-table';
import { Button } from '@/components/ui/button';
import { Edit, Trash2, Folder } from 'lucide-react';
import { useBusinessContext } from '@/context/BusinessContext';
import type { ICategory } from '@/types';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { CategoryForm } from '@/components/pages/categories/CategoryForm';

export function Categories() {
  const [isCategoryFormDialogOpen, setIsCategoryFormDialogOpen] = useState(false);
  const [editingCategory, setEditingCategory] = useState<ICategory | null>(null);
  const {
    entities: { categories },
    loading,
    contextError,
    fetchActions: { fetchCategories },
  } = useBusinessContext();

  useEffect(() => {
    fetchCategories();
  }, []);

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'claim':
        return 'bg-blue-100 text-blue-800';
      case 'service':
        return 'bg-green-100 text-green-800';
      case 'damage':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const columns: Column<any>[] = [
    {
      key: 'name',
      header: 'Category Name',
      sortable: true,
      render: (value, row) => (
        <div className="flex items-center gap-3">
          <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${getTypeColor(row.type)}`}>
            <Folder className="h-4 w-4" />
          </div>
          <div>
            <div className="font-medium">{value}</div>
            {row.parentId && (
              <div className="text-sm text-muted-foreground">Subcategory</div>
            )}
          </div>
        </div>
      )
    },
    {
      key: 'actions',
      header: 'Actions',
      render: (_, row) => (
        <div className="flex items-center justify-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              setEditingCategory(row);
              setIsCategoryFormDialogOpen(true);
            }}
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
            }}
            className='cursor-pointer'
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      )
    }
  ];

  return (
    <div className="space-y-6">
      <PageHeader
        title="Categories"
        description="Manage assignment categories, service types, and damage classifications"
      >
        {/*
        <Button onClick={() => {
          setEditingCategory(null);
          setIsCategoryFormDialogOpen(true);
        }}>
          <Plus className="h-4 w-4 mr-2" />
          Add Category
        </Button> 
        */}
      </PageHeader>

      <PageContent>
        {contextError && (
          <div className="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
            <p>Error: {contextError}</p>
          </div>
        )}
        <DataTable
          data={categories}
          columns={columns}
          searchPlaceholder="Search categories..."
          onRowClick={(category) => console.log('View category:', category)}
          loading={loading.categories}
        />
      </PageContent>

      {/* Add/Edit Dialog */}
      <Dialog open={isCategoryFormDialogOpen} onOpenChange={setIsCategoryFormDialogOpen}>
        <DialogContent className="max-w-[90vw] max-h-[90vh] overflow-y-auto" aria-describedby={undefined}>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {editingCategory ? 'Edit Category' : 'Add New Category'}
            </DialogTitle>
          </DialogHeader>
          <CategoryForm
            mode={editingCategory ? 'edit' : 'create'}
            initialData={editingCategory || undefined}
            onCancel={() => setIsCategoryFormDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
}
