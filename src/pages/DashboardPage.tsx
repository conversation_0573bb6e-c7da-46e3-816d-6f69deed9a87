import { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, PageContent } from '@/components/custom-ui/page-header';
import { MetricCard, MetricGrid } from '@/components/custom-ui/metric-card';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import {
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart
} from 'recharts';
import {
  ClipboardList,
  CheckCircle,
  Users,
  DollarSign,
  Filter
} from 'lucide-react';
import { format } from 'date-fns';
import type { DateRange, DashboardMetrics } from '@/pages/types/dashboard';

// Mock Data
import { v4 as uuidv4 } from 'uuid';
export const mockCarriers: any[] = [
  {
    id: uuidv4(),
    name: "State Farm Insurance",
    code: "<PERSON>",
    contactPerson: "<PERSON>",
    email: "<EMAIL>",
    phone: "(*************",
    address: {
      street: "1 State Farm Plaza",
      city: "Bloomington",
      state: "IL",
      zipCode: "61710"
    },
    policyTypes: ["Auto", "Home", "Life", "Commercial"],
    isActive: true,
    rating: 4.5,
    totalAssignments: 1247,
    avgProcessingTime: 12,
    createdAt: new Date('2020-01-15'),
    updatedAt: new Date('2024-06-15')
  },
  {
    id: uuidv4(),
    name: "Allstate Insurance",
    code: "AS",
    contactPerson: "Sarah Johnson",
    email: "<EMAIL>",
    phone: "(*************",
    address: {
      street: "2775 Sanders Rd",
      city: "Northbrook",
      state: "IL",
      zipCode: "60062"
    },
    policyTypes: ["Auto", "Home", "Renters", "Commercial"],
    isActive: true,
    rating: 4.2,
    totalAssignments: 892,
    avgProcessingTime: 15,
    createdAt: new Date('2020-03-20'),
    updatedAt: new Date('2024-06-10')
  },
  {
    id: uuidv4(),
    name: "Progressive Insurance",
    code: "PG",
    contactPerson: "Michael Chen",
    email: "<EMAIL>",
    phone: "(*************",
    address: {
      street: "6300 Wilson Mills Rd",
      city: "Mayfield Village",
      state: "OH",
      zipCode: "44143"
    },
    policyTypes: ["Auto", "Motorcycle", "Commercial Auto"],
    isActive: true,
    rating: 4.0,
    totalAssignments: 1156,
    avgProcessingTime: 10,
    createdAt: new Date('2020-05-10'),
    updatedAt: new Date('2024-06-20')
  },
  {
    id: uuidv4(),
    name: "GEICO Insurance",
    code: "GC",
    contactPerson: "Lisa Rodriguez",
    email: "<EMAIL>",
    phone: "(*************",
    address: {
      street: "1 GEICO Plaza",
      city: "Chevy Chase",
      state: "MD",
      zipCode: "20815"
    },
    policyTypes: ["Auto", "Motorcycle", "Home", "Renters"],
    isActive: true,
    rating: 4.3,
    totalAssignments: 2134,
    avgProcessingTime: 8,
    createdAt: new Date('2019-11-05'),
    updatedAt: new Date('2024-06-25')
  },
  {
    id: uuidv4(),
    name: "Liberty Mutual",
    code: "LM",
    contactPerson: "David Thompson",
    email: "<EMAIL>",
    phone: "(*************",
    address: {
      street: "175 Berkeley St",
      city: "Boston",
      state: "MA",
      zipCode: "02116"
    },
    policyTypes: ["Auto", "Home", "Life", "Commercial", "Workers Comp"],
    isActive: true,
    rating: 4.1,
    totalAssignments: 756,
    avgProcessingTime: 14,
    createdAt: new Date('2021-02-12'),
    updatedAt: new Date('2024-06-18')
  },
  {
    id: uuidv4(),
    name: "Farmers Insurance",
    code: "FI",
    contactPerson: "Amanda Wilson",
    email: "<EMAIL>",
    phone: "(*************",
    address: {
      street: "4680 Wilshire Blvd",
      city: "Los Angeles",
      state: "CA",
      zipCode: "90010"
    },
    policyTypes: ["Auto", "Home", "Life", "Business"],
    isActive: true,
    rating: 3.9,
    totalAssignments: 634,
    avgProcessingTime: 16,
    createdAt: new Date('2021-08-30'),
    updatedAt: new Date('2024-06-12')
  },
  {
    id: uuidv4(),
    name: "Nationwide Insurance",
    code: "NW",
    contactPerson: "Robert Garcia",
    email: "<EMAIL>",
    phone: "(*************",
    address: {
      street: "1 Nationwide Plaza",
      city: "Columbus",
      state: "OH",
      zipCode: "43215"
    },
    policyTypes: ["Auto", "Home", "Life", "Pet", "Commercial"],
    isActive: false,
    rating: 4.4,
    totalAssignments: 423,
    avgProcessingTime: 11,
    createdAt: new Date('2022-01-15'),
    updatedAt: new Date('2024-05-20')
  }
];
const mockAdjusters: any[] = [
  {
    id: uuidv4(),
    firstName: "Emily",
    lastName: "Carter",
    email: "<EMAIL>",
    phone: "(*************",
    specializations: ["Auto Assignments", "Property Damage", "Liability"],
    assignedCarriers: [mockCarriers[0].id, mockCarriers[1].id],
    licenseNumber: "ADJ-12345",
    licenseExpiry: new Date('2025-12-31'),
    isActive: true,
    rating: 4.8,
    totalAssignments: 156,
    avgCompletionTime: 8,
    address: {
      street: "123 Main St",
      city: "Chicago",
      state: "IL",
      zipCode: "60601"
    },
    emergencyContact: {
      name: "James Carter",
      phone: "(*************",
      relationship: "Spouse"
    },
    createdAt: new Date('2021-03-15'),
    updatedAt: new Date('2024-06-20')
  },
  {
    id: uuidv4(),
    firstName: "Marcus",
    lastName: "Johnson",
    email: "<EMAIL>",
    phone: "(*************",
    specializations: ["Commercial Claims", "Workers Compensation", "Fraud Investigation"],
    assignedCarriers: [mockCarriers[2].id, mockCarriers[3].id],
    licenseNumber: "ADJ-23456",
    licenseExpiry: new Date('2026-06-30'),
    isActive: true,
    rating: 4.6,
    totalAssignments: 203,
    avgCompletionTime: 12,
    address: {
      street: "456 Oak Ave",
      city: "Cleveland",
      state: "OH",
      zipCode: "44101"
    },
    emergencyContact: {
      name: "Maria Johnson",
      phone: "(*************",
      relationship: "Sister"
    },
    createdAt: new Date('2020-08-20'),
    updatedAt: new Date('2024-06-18')
  },
  {
    id: uuidv4(),
    firstName: "Sarah",
    lastName: "Williams",
    email: "<EMAIL>",
    phone: "(*************",
    specializations: ["Property Claims", "Water Damage", "Fire Damage"],
    assignedCarriers: [mockCarriers[0].id, mockCarriers[4].id],
    licenseNumber: "ADJ-34567",
    licenseExpiry: new Date('2025-09-15'),
    isActive: true,
    rating: 4.9,
    totalAssignments: 189,
    avgCompletionTime: 7,
    address: {
      street: "789 Pine St",
      city: "Boston",
      state: "MA",
      zipCode: "02101"
    },
    emergencyContact: {
      name: "David Williams",
      phone: "(*************",
      relationship: "Father"
    },
    createdAt: new Date('2021-01-10'),
    updatedAt: new Date('2024-06-22')
  }
];

export const mockDashboardMetrics: DashboardMetrics = {
  openAssignments: 156,
  closedAssignments: 1247,
  activeConsultations: 89,
  totalRevenue: 2456789,
  avgProcessingTime: 11.5,
  carrierPerformance: [
    {
      carrierId: mockCarriers[0].id,
      carrierName: mockCarriers[0].name,
      totalAssignments: 1247,
      avgProcessingTime: 12,
      satisfactionRating: 4.5,
      revenue: 856432
    },
    {
      carrierId: mockCarriers[1].id,
      carrierName: mockCarriers[1].name,
      totalAssignments: 892,
      avgProcessingTime: 15,
      satisfactionRating: 4.2,
      revenue: 634521
    }
  ],
  recentActivity: [
    {
      id: uuidv4(),
      type: "assignment_created",
      title: "New Assignment Created",
      description: "ASG-2024-001234 - Auto collision assignment",
      timestamp: new Date('2024-06-20T10:30:00'),
      userId: "user-1",
      userName: "Emily Carter"
    },
    {
      id: uuidv4(),
      type: "assignment_assigned",
      title: "Assignment Assigned",
      description: "ASG-2024-001235 assigned to Marcus Johnson",
      timestamp: new Date('2024-06-20T09:15:00'),
      userId: "user-2",
      userName: "System"
    }
  ],
  assignmentsByStatus: [
    { status: "Open", count: 156, percentage: 35 },
    { status: "In Progress", count: 89, percentage: 20 },
    { status: "Under Review", count: 67, percentage: 15 },
    { status: "Closed", count: 134, percentage: 30 }
  ],
  assignmentsByMonth: [
    { month: "Jan", openAssignments: 45, closedAssignments: 38, revenue: 185000 },
    { month: "Feb", openAssignments: 52, closedAssignments: 41, revenue: 198000 },
    { month: "Mar", openAssignments: 48, closedAssignments: 45, revenue: 210000 },
    { month: "Apr", openAssignments: 61, closedAssignments: 52, revenue: 225000 },
    { month: "May", openAssignments: 58, closedAssignments: 49, revenue: 215000 },
    { month: "Jun", openAssignments: 67, closedAssignments: 55, revenue: 245000 }
  ],
  topAdjusters: [
    {
      adjusterId: mockAdjusters[0].id,
      adjusterName: `${mockAdjusters[0].firstName} ${mockAdjusters[0].lastName}`,
      totalAssignments: 156,
      avgCompletionTime: 8,
      rating: 4.8,
      revenue: 425000
    },
    {
      adjusterId: mockAdjusters[1].id,
      adjusterName: `${mockAdjusters[1].firstName} ${mockAdjusters[1].lastName}`,
      totalAssignments: 203,
      avgCompletionTime: 12,
      rating: 4.6,
      revenue: 567000
    }
  ]
};

export function Dashboard() {
  const [dateRange, setDateRange] = useState<DateRange>('monthly');

  const metrics = mockDashboardMetrics;
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatNumber = (value: number) => {
    return new Intl.NumberFormat('en-US').format(value);
  };

  return (
    <div className="space-y-6">
      <PageHeader
        title="Dashboard"
        description="Overview of assignments, performance metrics, and key insights"
      >
        <div className="flex items-center gap-4">
          <Select value={dateRange} onValueChange={(value: DateRange) => setDateRange(value)}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="daily">Daily</SelectItem>
              <SelectItem value="weekly">Weekly</SelectItem>
              <SelectItem value="monthly">Monthly</SelectItem>
              <SelectItem value="quarterly">Quarterly</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            Filters
          </Button>
        </div>
      </PageHeader>

      <PageContent>
        {/* Key Metrics */}
        <MetricGrid className="mb-8">
          <MetricCard
            title="Open Assignments"
            value={formatNumber(metrics.openAssignments)}
            change={{ value: 12, type: 'increase', period: 'last month' }}
            icon={ClipboardList}
            trend="up"
          />
          <MetricCard
            title="Closed Assignments"
            value={formatNumber(metrics.closedAssignments)}
            change={{ value: 8, type: 'increase', period: 'last month' }}
            icon={CheckCircle}
            trend="up"
          />
          <MetricCard
            title="Active Consultations"
            value={formatNumber(metrics.activeConsultations)}
            change={{ value: 3, type: 'decrease', period: 'last week' }}
            icon={Users}
            trend="down"
          />
          <MetricCard
            title="Total Revenue"
            value={formatCurrency(metrics.totalRevenue)}
            change={{ value: 15, type: 'increase', period: 'last quarter' }}
            icon={DollarSign}
            trend="up"
          />
        </MetricGrid>

        <div className="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
          {/* Assignments by Status */}
          <Card className="col-span-1 md:col-span-1 lg:col-span-1">
            <CardHeader>
              <CardTitle>Assignments by Status</CardTitle>
              <CardDescription>Distribution of current assignment statuses</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={metrics.assignmentsByStatus}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percentage }) => `${name} ${percentage}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="count"
                  >
                    {metrics.assignmentsByStatus.map((_, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Monthly Assignments Trend */}
          <Card className="col-span-1 md:col-span-2 lg:col-span-2">
            <CardHeader>
              <CardTitle>Assignments Trend</CardTitle>
              <CardDescription>Monthly open vs closed assignments</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={metrics.assignmentsByMonth}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip formatter={(value, name) => [formatNumber(Number(value)), name]} />
                  <Area
                    type="monotone"
                    dataKey="openAssignments"
                    stackId="1"
                    stroke="#8884d8"
                    fill="#8884d8"
                    name="Open Assignments"
                  />
                  <Area
                    type="monotone"
                    dataKey="closedAssignments"
                    stackId="1"
                    stroke="#82ca9d"
                    fill="#82ca9d"
                    name="Closed Assignments"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Revenue Trend */}
          <Card className="col-span-1 md:col-span-2 lg:col-span-2">
            <CardHeader>
              <CardTitle>Revenue Trend</CardTitle>
              <CardDescription>Monthly revenue performance</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={metrics.assignmentsByMonth}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis tickFormatter={(value) => formatCurrency(value)} />
                  <Tooltip formatter={(value) => [formatCurrency(Number(value)), 'Revenue']} />
                  <Line
                    type="monotone"
                    dataKey="revenue"
                    stroke="#ff7300"
                    strokeWidth={3}
                    dot={{ fill: '#ff7300' }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Top Adjusters */}
          <Card className="col-span-1 md:col-span-1 lg:col-span-1">
            <CardHeader>
              <CardTitle>Top Adjusters</CardTitle>
              <CardDescription>Performance by adjuster</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {metrics.topAdjusters.map((adjuster, index) => (
                  <div key={adjuster.adjusterId} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center text-sm font-medium">
                        {index + 1}
                      </div>
                      <div>
                        <p className="font-medium">{adjuster.adjusterName}</p>
                        <p className="text-sm text-muted-foreground">
                          {adjuster.totalAssignments} assignments
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">{formatCurrency(adjuster.revenue)}</p>
                      <p className="text-sm text-muted-foreground">
                        ⭐ {adjuster.rating}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>Latest updates and actions</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {metrics.recentActivity.map((activity) => (
                <div key={activity.id} className="flex items-start gap-4 p-4 rounded-lg border">
                  <div className="w-2 h-2 rounded-full bg-primary mt-2"></div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">{activity.title}</h4>
                      <span className="text-sm text-muted-foreground">
                        {format(activity.timestamp, 'MMM dd, HH:mm')}
                      </span>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      {activity.description}
                    </p>
                    {activity.userName && (
                      <p className="text-xs text-muted-foreground mt-1">
                        by {activity.userName}
                      </p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </PageContent>
    </div>
  );
}
