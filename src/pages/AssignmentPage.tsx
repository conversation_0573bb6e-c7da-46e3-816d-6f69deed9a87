import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Plus, Edit, Trash2, Calendar, User, Building2 } from 'lucide-react';
import { format } from 'date-fns';
import { PageHeader, PageContent } from '@/components/custom-ui/page-header';
import { DataTable, type Column } from '@/components/custom-ui/data-table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/custom-ui/badge';
import { Modal } from '@/components/custom-ui/modal';
import { AssignmentForm } from '@/components/pages/assignment/AssignmentForm';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { EmptyState, LoadingSpinner } from '@/components/custom-ui/loading';
import { useBusinessContext } from '@/context/BusinessContext';
import { useAssignmentOperations } from '@/components/pages/assignment/useAssignmentOperations';
import type {
  IAssignment,
} from '@/types';

export function AssignmentPage() {
  const navigate = useNavigate();
  const [isAssignmentFormDialogOpen, setIsAssignmentFormDialogOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [editingAssignment, setEditingAssignment] = useState<IAssignment | null>(null);
  const [deletingAssignment, setDeletingAssignment] = useState<IAssignment | null>(null);

  const {
    entities: {
      assignments
    },
    contextError,
    loading,
    fetchActions: {
      fetchAssignments,
    },
  } = useBusinessContext();

  const { handleSave, handleEdit, handleDelete } = useAssignmentOperations({
    onSuccess: () => {
      setIsAssignmentFormDialogOpen(false);
      setEditingAssignment(null);
      fetchAssignments();
    }
  });

  useEffect(() => {
    fetchAssignments();
  }, []);

  const getStatusBadgeVariant = (status?: string) => {
    switch (status) {
      case 'active': return 'default';
      case 'close': return 'secondary';
      case 'pending': return 'outline';
      default: return 'default';
    }
  };

  const columns: Column<IAssignment>[] = [
    {
      key: "updatedAt",
      header: 'Updated',
      sortable: true,
      render: (value) => (
        <div className="flex items-center justify-center gap-2 text-sm">
          <Calendar className="h-3 w-3" />
          {typeof value === 'string' && value ? format(new Date(value), 'MMM dd, yyyy') : 'N/A'}
        </div>
      )
    },
    {
      key: 'assignmentLead',
      header: 'Assignment Lead',
      sortable: true,
      render: (_, row) => (
        <div className="flex items-center justify-center gap-3">
          <div>
            <div className="font-medium">{typeof row.assignmentLead?.name === 'string' ? row.assignmentLead.name : 'N/A'}</div>
            <div className="text-sm text-muted-foreground">{typeof row.assignmentLead?.email === 'string' ? row.assignmentLead.email : 'N/A'}</div>
          </div>
        </div>
      )
    },
    {
      key: 'assignmentName',
      header: 'Assignment Name',
      sortable: true,
      render: (value) => (
        <div className="flex items-center justify-center gap-3">
          <div>
            <div className="font-medium">{value || 'N/A'}</div>
            <div className="text-sm text-muted-foreground">Insured - Number</div>
          </div>
        </div>
      )
    },
    {
      key: 'carrierId',
      header: 'Carriers',
      render: (_, row) => (
        <div className="flex items-center justify-center gap-2">
          <Building2 className="h-4 w-4" />
          <span className="text-sm">{row.carriers?.length || 0}</span>
        </div>
      )
    },
    {
      key: 'adjusterId',
      header: 'Adjuster',
      render: (_, row) => (
        <div className="flex items-center justify-center gap-2">
          <User className="h-4 w-4" />
          <span className="text-sm">{typeof row.adjuster?.name === 'string' ? row.adjuster.name : 'N/A'}</span>
        </div>
      )
    },
    {
      key: 'status',
      header: 'Status',
      sortable: true,
      render: (value) => (
        <Badge variant={getStatusBadgeVariant(value)}>
          {typeof value === 'string' && value ? value.charAt(0).toUpperCase() + value.slice(1).replace('-', ' ') : 'Unknown'}
        </Badge>
      )
    },
    {
      key: 'dateOfLoss',
      header: 'Date of Loss',
      sortable: true,
      render: (value) => (
        <div className="flex items-center justify-center gap-2 text-sm">
          <Calendar className="h-3 w-3" />
          {typeof value === 'string' && value ? format(new Date(value), 'MMM dd, yyyy') : 'N/A'}
        </div>
      )
    },
    {
      key: 'actions',
      header: 'Actions',
      render: (_, row) => (
        <div className="flex items-center justify-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              setEditingAssignment(row);
              setIsAssignmentFormDialogOpen(true);
            }}
            className='cursor-pointer'
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="destructive"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              setDeletingAssignment(row);
              setIsDeleteModalOpen(true);
            }}
            className='cursor-pointer'
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      )
    }
  ];

  const handleView = (assignment: IAssignment) => {
    navigate(`/assignments/${assignment.id}`);
  };

  return (
    <div className="space-y-6">
      <PageHeader
        title="Assignments Management"
        description="Manage insurance assignments and processing"
      >
        <Button onClick={() => {
          setEditingAssignment(null);
          setIsAssignmentFormDialogOpen(true);
        }}>
          <Plus className="h-4 w-4 mr-2" />
          New Assignment
        </Button>
      </PageHeader>

      <PageContent>
        {
          loading.assignments ? (
            <div className="flex justify-center items-center h-96">
              <LoadingSpinner />
            </div>
          ) : contextError ? (
            <div className="flex justify-center items-center h-96">
              <EmptyState
                title="Error loading assignments"
                description="There was an error loading the assignments. Please try again later."
                action={<Button onClick={fetchAssignments}>Retry</Button>}
              />
            </div>
          ) : assignments.length === 0 ? (
            <div className="flex justify-center items-center h-96">
              <EmptyState
                title="No assignments found"
                description="There are no assignments to display."
                action={<Button onClick={fetchAssignments}>Refresh</Button>}
              />
            </div>
          ) : null
        }
        <DataTable
          data={assignments}
          columns={columns}
          searchPlaceholder="Search assignments..."
          onRowClick={(assignment) => handleView(assignment)}
        />
      </PageContent>

      {/* Assignment Form Dialog */}
      <Dialog open={isAssignmentFormDialogOpen} onOpenChange={setIsAssignmentFormDialogOpen}>
        <DialogContent className="w-[50vw] max-h-[90vh] overflow-y-auto" aria-describedby={undefined}>
          <DialogHeader>
            <DialogTitle>
              {editingAssignment ? 'Edit Assignment' : 'Create New Assignment'}
            </DialogTitle>
          </DialogHeader>
          <AssignmentForm
            initialData={editingAssignment || undefined}
            onCancel={() => setIsAssignmentFormDialogOpen(false)}
            onSubmit={editingAssignment ? handleEdit : handleSave}
          />
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Modal */}
      {deletingAssignment && (
        <Modal
          isOpen={isDeleteModalOpen}
          onClose={() => setIsDeleteModalOpen(false)}
          title="Delete Assignment"
          size="sm"
        >
          <div className="space-y-4">
            <p>Are you sure you want to delete assignment <strong>{deletingAssignment.assignmentName || deletingAssignment.claimNumber}</strong>?</p>
            <p className="text-sm text-muted-foreground">This action cannot be undone.</p>
          </div>
          <div className="flex justify-end gap-2 mt-6">
            <Button
              variant="outline"
              onClick={() => setIsDeleteModalOpen(false)}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={() => {
                handleDelete(deletingAssignment);
                setIsDeleteModalOpen(false);
                setDeletingAssignment(null);
              }}
            >
              Delete
            </Button>
          </div>
        </Modal>
      )}


    </div>
  );
}
