import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"
import { format, parseISO, isValid } from "date-fns"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function isoToDateTimeLocal(isoString: string | null | undefined): string {
  if (!isoString) return "";

  try {
    const date = parseISO(isoString);
    if (!isValid(date)) return "";

    const localDate = new Date(date.getTime() - date.getTimezoneOffset() * 60000);
    return localDate.toISOString().slice(0, 16);
  } catch (error) {
    console.warn("Error converting ISO string to datetime-local:", error);
    return "";
  }
}

export function dateTimeLocalToISO(dateTimeLocal: string | null | undefined): string | null {
  if (!dateTimeLocal) return null;

  try {
    const date = new Date(dateTimeLocal);
    if (!isValid(date)) return null;

    return date.toISOString();
  } catch (error) {
    console.warn("Error converting datetime-local to ISO:", error);
    return null;
  }
}

export function formatDateForDisplay(
  isoString: string | null | undefined,
  formatString: string = "MMM dd, yyyy 'at' HH:mm"
): string {
  if (!isoString) return "N/A";

  try {
    const date = parseISO(isoString);
    if (!isValid(date)) return "N/A";

    return format(date, formatString);
  } catch (error) {
    console.warn("Error formatting date for display:", error);
    return "N/A";
  }
}

export function isValidISODate(isoString: string | null | undefined): boolean {
  if (!isoString) return false;

  try {
    const date = parseISO(isoString);
    return isValid(date);
  } catch (error) {
    return false;
  }
}

export function getCurrentISODate(): string {
  return new Date().toISOString();
}

export function dateToISO(date: Date | null | undefined): string | null {
  if (!date || !isValid(date)) return null;
  return date.toISOString();
}
